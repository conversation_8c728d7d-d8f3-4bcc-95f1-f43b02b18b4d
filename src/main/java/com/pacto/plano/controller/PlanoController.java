package com.pacto.plano.controller;


import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.plano.config.swagger.respostas.categoria.EnvelopeRespostaCategoria;
import com.pacto.plano.config.swagger.respostas.pacote.EnvelopeRespostaListPacote;
import com.pacto.plano.config.swagger.respostas.plano.EnvelopeRespostaListPlano;
import com.pacto.plano.config.swagger.respostas.plano.EnvelopeRespostaPlano;
import com.pacto.plano.config.swagger.respostas.plano.condicao.EnvelopeRespostaCondicaoPagamentoPlanoDuracao;
import com.pacto.plano.config.swagger.respostas.plano.contrato.EnvelopeRespostaListPlanoContrato;
import com.pacto.plano.config.swagger.respostas.plano.contrato.EnvelopeRespostaPlanoContrato;
import com.pacto.plano.config.swagger.respostas.variaveis.EnvelopeRespostaBoolean;
import com.pacto.plano.config.swagger.respostas.variaveis.EnvelopeRespostaInteger;
import com.pacto.plano.dto.CondicaoPagamentoPlanoDuracaoDTO;
import com.pacto.plano.dto.filtros.FiltroPlanoJSON;
import com.pacto.plano.dto.plano.PlanoContratoDTO;
import com.pacto.plano.dto.plano.PlanoDTO;
import com.pacto.plano.services.interfaces.PlanoService;
import com.pacto.plano.util.ExceptionHandlerUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pacto.plano.config.swagger.SwaggerTags.PLANO;

@RestController
@RequestMapping("/planos")
@Tag(name = PLANO)
public class PlanoController {

    private final PlanoService planoService;

    @Autowired
    public PlanoController(PlanoService planoService) {
        this.planoService = planoService;
    }

    @Operation(
            summary = "Consultar planos da academia",
            description = "Consulta as informações dos planos, podendo aplicar filtros durante a busca.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pela descrição ou pelo código identificador do plano.</li>" +
                                    "<li><strong>ingressoate:</strong> Filtra pela data máxima de ingresso do plano</li>" +
                                    "<li><strong>vigenciaate:</strong> Filtra pela data máxima de vigência do plano</li>" +
                                    "<li><strong>site:</strong> Filtra pelos planos do site (Deve ser informado como true ou false)</li>" +
                                    "<li><strong>tipo:</strong> Filtra pelo tipo do plano, os valores disponíveis são:" +
                                    "<ul>" +
                                    "<li>normal</li>" +
                                    "<li>recorrencia</li>" +
                                    "<li>credito</li>" +
                                    "<li>personal</li>" +
                                    "<li>avancado</li>" +
                                    "</ul>" +
                                    "</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"MUSCULAÇÃO\",\"ingressoate\":\"2025-05-27\",\"vigenciaate\":\"2025-12-31\",\"site\":\"tipo\":\"normal\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "app", description = "Indica se deve buscar por planos do app", example = "false"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.<br/>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPlano.PLANO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @RequestParam(value = "app", required = false, defaultValue = "false") Boolean app,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroPlanoJSON filtroPlanoJSON = new FiltroPlanoJSON(filtros);
            return ResponseEntityFactory.ok(planoService.findAll(filtroPlanoJSON, paginadorDTO, app), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar plano da academia",
            description = "Consulta as informações de um plano pelo código identificador dele.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do plano que será consultado", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaPlano.PLANO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> plano(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(planoService.plano(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Clonar plano da academia",
            description = "Clona as informações de um plano pelo código identificador dele.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do plano que será clonado", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaPlano.PLANO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}/clonar")
    public ResponseEntity<EnvelopeRespostaDTO> clonarPlano(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(planoService.clonarPlano(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Replicar plano da academia",
            description = "Replica as informações de um plano.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para replicar um novo registro. <strong>É necessário enviar sem o atributo codigo para criar</strong>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = PlanoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaPlano.PLANO_ATRIBUTOS)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaPlano.PLANO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "replicar", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarPlano(@RequestBody PlanoDTO planoDTO) {
        try {
            planoDTO.setReplicar(true);
            return ResponseEntityFactory.ok(planoService.saveOrUpdate(planoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ExceptionHandlerUtil.handleException(e);
        }
    }

    @Operation(
            summary = "Cadastrar plano da academia",
            description = "Cadastra um novo plano para a academia.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para cadastrar um novo registro. <strong>É necessário enviar sem o atributo codigo para criar</strong>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = PlanoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaPlano.PLANO_ATRIBUTOS)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaPlano.PLANO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @ResponseBody
    @PostMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> save(@RequestBody PlanoDTO planoDTO) {
        try {
            return ResponseEntityFactory.ok(planoService.saveOrUpdate(planoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ExceptionHandlerUtil.handleException(e);
        }
    }

    @Operation(
            summary = "Incluir condições de pagamentos para os planos da academia",
            description = "Inclui condições de pagamento para os planos da academia.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da Condição de Pagamento que está vinculada aos planos e que será atualizada ou incluída as novas condições", example = "1", required = true)
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para incluir as condições de pagamentos. Pode ser informado uma lista com várias condições para inclusão.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CondicaoPagamentoPlanoDuracaoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaCondicaoPagamentoPlanoDuracao.CONDICAO_PAGAMENTO_PLANO_DURACAO_INCLUIR)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPlano.PLANO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/planoCondicaoPagamento", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirPlanoCondicoesPagamento(@RequestBody List<CondicaoPagamentoPlanoDuracaoDTO> condicaoPagamentoPlanoDuracao, @PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(planoService.saveOrUpdatePlanosCondicaoPagamento(condicaoPagamentoPlanoDuracao, id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Deletar plano da academia",
            description = "Deleta as informações de um plano pelo código identificador dele.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do plano que será deletado", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarPlano(@PathVariable Integer id) {
        try {
            planoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar nome e código dos planos da academia",
            description = "Consulta apenasn o nome e os códigos dos planos da academia, podendo aplicar filtros durante a busca.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pela descrição ou pelo código identificador do plano.</li>" +
                                    "<li><strong>ingressoate:</strong> Filtra pela data máxima de ingresso do plano</li>" +
                                    "<li><strong>vigenciaate:</strong> Filtra pela data máxima de vigência do plano</li>" +
                                    "<li><strong>site:</strong> Filtra pelos planos do site (Deve ser informado como true ou false)</li>" +
                                    "<li><strong>tipo:</strong> Filtra pelo tipo do plano, os valores disponíveis são:" +
                                    "<ul>" +
                                    "<li>normal</li>" +
                                    "<li>recorrencia</li>" +
                                    "<li>credito</li>" +
                                    "<li>personal</li>" +
                                    "<li>avancado</li>" +
                                    "</ul>" +
                                    "</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"MUSCULAÇÃO\",\"ingressoate\":\"2025-05-27\",\"vigenciaate\":\"2025-12-31\",\"site\":\"tipo\":\"normal\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.<br/>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPlano.PLANO_RESPOSTA_NOME_CODIGO_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/only-cod-name")
    public ResponseEntity<EnvelopeRespostaDTO> findAllCodName(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                              @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroPlanoJSON filtroPlanoJSON = new FiltroPlanoJSON(filtros);
            return ResponseEntityFactory.ok(planoService.findAllCodName(filtroPlanoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar planos por condição de pagamento",
            description = "Consulta as informações dos planos pela condição de pagamento associada a ele.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pela descrição ou pelo código identificador do plano.</li>" +
                                    "<li><strong>ingressoate:</strong> Filtra pela data máxima de ingresso do plano</li>" +
                                    "<li><strong>vigenciaate:</strong> Filtra pela data máxima de vigência do plano</li>" +
                                    "<li><strong>site:</strong> Filtra pelos planos do site (Deve ser informado como true ou false)</li>" +
                                    "<li><strong>tipo:</strong> Filtra pelo tipo do plano, os valores disponíveis são:" +
                                    "<ul>" +
                                    "<li>normal</li>" +
                                    "<li>recorrencia</li>" +
                                    "<li>credito</li>" +
                                    "<li>personal</li>" +
                                    "<li>avancado</li>" +
                                    "</ul>" +
                                    "</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"MUSCULAÇÃO\",\"ingressoate\":\"2025-05-27\",\"vigenciaate\":\"2025-12-31\",\"site\":\"tipo\":\"normal\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "idCondicaoPagamento", description = "Código identificador da condição de pagamento que está associada aos planos", example = "1"),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPlano.PLANO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/find-by-condicao-pagamento")
    public ResponseEntity<EnvelopeRespostaDTO> findByIdCondicaoPagamento(@RequestParam(value = "filters", required = false) JSONObject filtros, @RequestParam(required = false) Integer idCondicaoPagamento) {
        try {
            FiltroPlanoJSON filtroPlanoJSON = new FiltroPlanoJSON(filtros);
            return ResponseEntityFactory.ok(planoService.findByIdCondicaoPagamento(filtroPlanoJSON, idCondicaoPagamento));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar quantidade de contratos ativos por plano",
            description = "Consulta a quantidade de contratos ativos por plano.",
            parameters = {
                    @Parameter(name = "idPlano", description = "Código identificador do plano que será consultado a quantidade de contratos ativos", example = "1")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaInteger.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaInteger.INTEGER_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/consultar-quantidade-contratos-ativos-por-plano")
    public ResponseEntity<EnvelopeRespostaDTO> consultarQuantidadeContratosAtivosPorPlano(@RequestParam(required = false) Integer idPlano) {
        try {
            return ResponseEntityFactory.ok(planoService.consultarQuantidadeContratosAtivosPorPlano(idPlano));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar pacotes de um plano",
            description = "Consulta os pacotes que estão vinculados a um plano.",
            parameters = {
                    @Parameter(name = "plano", description = "Código identificador do plano que será consultado os pacotes", example = "1")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPacote.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPacote.PACOTE_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/pacotes")
    public ResponseEntity<EnvelopeRespostaDTO> consultarPacotes(Integer plano) {
        return ResponseEntityFactory.ok(planoService.consultarPacotes(plano));
    }

    @Operation(
            summary = "Consultar categorias de um plano",
            description = "Consulta as categorias que estão vinculados a um plano.",
            parameters = {
                    @Parameter(name = "plano", description = "Código identificador do plano que será consultado as categoriais", example = "1")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPacote.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaCategoria.CATEGORIA_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/categorias")
    public ResponseEntity<EnvelopeRespostaDTO> consultarCategoriasPlano(Integer plano) {
        return ResponseEntityFactory.ok(planoService.consultarCategoria(plano));
    }

    @Operation(
            summary = "Consultar planos e as condições de pagamentos",
            description = "Consulta as informações dos planos e as condições de pagamentos vinculadas a ele, podendo aplicar filtros durante a busca.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pela descrição ou pelo código identificador do plano.</li>" +
                                    "<li><strong>ingressoate:</strong> Filtra pela data máxima de ingresso do plano</li>" +
                                    "<li><strong>vigenciaate:</strong> Filtra pela data máxima de vigência do plano</li>" +
                                    "<li><strong>site:</strong> Filtra pelos planos do site (Deve ser informado como true ou false)</li>" +
                                    "<li><strong>tipo:</strong> Filtra pelo tipo do plano, os valores disponíveis são:" +
                                    "<ul>" +
                                    "<li>normal</li>" +
                                    "<li>recorrencia</li>" +
                                    "<li>credito</li>" +
                                    "<li>personal</li>" +
                                    "<li>avancado</li>" +
                                    "</ul>" +
                                    "</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"MUSCULAÇÃO\",\"ingressoate\":\"2025-05-27\",\"vigenciaate\":\"2025-12-31\",\"site\":\"tipo\":\"normal\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "app", description = "Indica se deve buscar por planos do app", example = "false"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.<br/>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPlano.PLANO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/consultar-planos-condicao-pagamento")
    public ResponseEntity<EnvelopeRespostaDTO> consultarPlanosCondicaoPagamento(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                @RequestParam(value = "app", required = false, defaultValue = "false") Boolean app,
                                                                                @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroPlanoJSON filtroPlanoJSON = new FiltroPlanoJSON(filtros);
            paginadorDTO.setSize(100L);
            return ResponseEntityFactory.ok(planoService.findAll(filtroPlanoJSON, paginadorDTO, app), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Aplicar texto \"contrato já lançado\" para um modelo de contrato",
            description = "Aplica texto contrato já lançado usado para um determinado modelo de contrato usando as informações enviadas como base.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para aplicar o texto.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = PlanoContratoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaPlanoContrato.PLANO_CONTRATO_ATRIBUTOS)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPlanoContrato.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPlanoContrato.PLANO_CONTRATO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/aplicar-para-contratos-lancados")
    public ResponseEntity<EnvelopeRespostaDTO> aplicarTextoContratoJaLancados(
            @RequestBody PlanoContratoDTO planoRequestDTO) throws Exception {
        try {
            return ResponseEntityFactory.ok(planoService.aplicarTextoContratoJaLancados(planoRequestDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Atualizar plano para avançado",
            description = "Atualiza o tipo do plano para avançado pelo código identificador dele.",
            parameters = {
                    @Parameter(name = "codigo", description = "Código identificador do plano que será alterado para avançado", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPlano.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaPlano.PLANO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @PutMapping("/atualizar-plano-para-avancado")
    public ResponseEntity<EnvelopeRespostaDTO> atualizarPlanoParaAvancado(@RequestParam Integer codigo) {
        try {
            return ResponseEntityFactory.ok(planoService.atualizarPlano(codigo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Validar existência de duração em um contrato",
            description = "Valida a existência de duração de um contrato. Caso a resposta do content seja true, o contrato de duração existe",
            parameters = {
                    @Parameter(name = "codigoPlano", description = "Código do plano que será consultado a existência do contrato de duração", example = "1"),
                    @Parameter(name = "duracao", description = "Número de meses da duração do contrato que será consultado", example = "12"),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaBoolean.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaBoolean.BOOLEAN_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/validar-contrato-duracao")
    public ResponseEntity<EnvelopeRespostaDTO> validarExisteContratoDuracao(@RequestParam Integer codigoPlano,
                                                                            @RequestParam Integer duracao) throws Exception {
        boolean existeContratoDuracao = planoService.existeContratoDuracao(codigoPlano, duracao);
        if (existeContratoDuracao) {
            throw new ServiceException("Duração não pode ser excluída, pois está vinculada a contratos");
        }
        return ResponseEntityFactory.ok();
    }
}
